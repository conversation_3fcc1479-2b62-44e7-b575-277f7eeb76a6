package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"
	"time"

	"solve-api/models"
	"solve-api/utils"
)

type DeepSeekRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

type DeepSeekResponse struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Message struct {
		Content string `json:"content"`
	} `json:"message"`
}

func AnalyzeQuestionWithDeepSeek(questionData string) (string, error) {
	url := "https://api.deepseek.com/v1/chat/completions"
	
prompt := fmt.Sprintf(`你是一个专业的题目分析专家，请分析以下题目并给出正确答案。

题目信息：
%s

【重要要求】：
1. 请通过互联网搜索找到这道题的标准答案
2. 不要自己推理，要找到已有的权威答案
3. 必须给出明确的答案选项

【强制要求】：
输出的内容标点符号必须是半角符号；
输出的格式必须是按照题型


【输出约定】
1. 判断题答案输出约定按照以下格式处理
答案：[Y：正确]或者[N：错误]
解析：[要完整的解析，但是不要说来源]

2. 单选题答案输出约定按照以下格式处理
答案：[A：真实答案内容]或者[B：真实答案内容]
解析：[要完整的解析，但是不要说来源]

3. 多选题答案输出约定按照以下格式处理
答案：[A：真实答案内容；B：真实答案内容；C：真实答案内容；D：真实答案内容]
解析：[要完整的解析，但是不要说来源]
    
`, questionData)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	request := DeepSeekRequest{
		Model:    "deepseek-chat",
		Messages: messages,
	}

	jsonData, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("JSON编码失败: %v", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %v", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+getDeepSeekAPIKey())

	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 2. 记录DeepSeek返回的原始数据到日志文件
	utils.LogDeepSeekRawResponse(questionData, string(body))

	// 打印DeepSeek原始响应用于调试（保留原有功能）
	fmt.Printf("🔍 DeepSeek原始响应: %s\n", string(body))

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("DeepSeek API错误: %s", string(body))
	}

	var response DeepSeekResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	if len(response.Choices) == 0 {
		return "", fmt.Errorf("DeepSeek返回空响应")
	}

	// 打印DeepSeek解析后的内容用于调试（保留原有功能）
	fmt.Printf("🔍 DeepSeek解析后内容: %s\n", response.Choices[0].Message.Content)

	return response.Choices[0].Message.Content, nil
}

// parseQwenExtraction 解析Qwen提取的题目信息
func parseQwenExtraction(content string) (*models.QuestionData, error) {
	// 使用统一的文本规范化工具
	content = utils.NormalizeText(content)
	lines := strings.Split(content, "\n")

	questionData := &models.QuestionData{
		Options: make(map[string]string),
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找冒号分隔符
		colonIndex := strings.Index(line, "：")
		if colonIndex == -1 {
			colonIndex = strings.Index(line, ":")
		}
		if colonIndex == -1 {
			continue
		}

		key := strings.TrimSpace(line[:colonIndex])
		value := strings.TrimSpace(line[colonIndex+1:])
		value = strings.Trim(value, "[]")
		value = utils.NormalizeText(value)

		// 标准化key，移除多余空格并统一格式
		key = strings.ReplaceAll(key, " ", "")

		// 标准化选项内容
		if strings.HasPrefix(key, "选项") {
			value = utils.NormalizeText(value)
		}

		switch key {
		case "题目类型":
			questionData.Type = value
		case "问题":
			questionData.Question = value
		case "选项A":
			questionData.Options["A"] = value
		case "选项B":
			questionData.Options["B"] = value
		case "选项C":
			questionData.Options["C"] = value
		case "选项D":
			questionData.Options["D"] = value
		// 支持判断题的Y/N选项格式
		case "选项Y":
			questionData.Options["Y"] = value
		case "选项N":
			questionData.Options["N"] = value
		// 支持其他可能的选项格式
		case "选项1":
			questionData.Options["1"] = value
		case "选项2":
			questionData.Options["2"] = value
		case "选项3":
			questionData.Options["3"] = value
		case "选项4":
			questionData.Options["4"] = value
		}
	}

	// 验证必要字段
	if questionData.Type == "" {
		return nil, fmt.Errorf("缺少题目类型")
	}
	if questionData.Question == "" {
		return nil, fmt.Errorf("缺少问题内容")
	}
	if len(questionData.Options) == 0 {
		return nil, fmt.Errorf("缺少选项")
	}

	// 3. 记录Qwen原始数据解析后的JSON到日志文件
	utils.LogQwenParsedJSON(content, questionData)

	return questionData, nil
}

// parseDeepSeekAnswer 解析DeepSeek的答案分析
func parseDeepSeekAnswer(content string) (string, string, error) {
	// 使用统一的文本规范化工具进行预处理
	content = utils.NormalizeText(content)

	if strings.TrimSpace(content) == "" {
		return "", "", fmt.Errorf("DeepSeek返回内容为空")
	}

	// 第二步：拆分答案和解析部分
	answerPart, analysisPart := splitAnswerAndAnalysisAdvanced(content)

	// 第三步：根据题型处理答案
	answer := extractAnswerByQuestionTypeAdvanced(answerPart, analysisPart)

	// 第四步：处理解析部分
	analysis := processAnalysisPartAdvanced(analysisPart, content)

	// 移除"参考来源"后面的所有内容
	analysis = removeReferenceSource(analysis)

	if answer == "" {
		return "", "", fmt.Errorf("未找到答案")
	}

	// 4. 记录DeepSeek原始数据解析后的JSON到日志文件
	utils.LogDeepSeekParsedJSON(content, answer, analysis)

	return answer, analysis, nil
}

// preprocessDeepSeekContentAdvanced 高级预处理DeepSeek内容
// 注意：此函数已被utils.NormalizeText替代，保留用于兼容性
func preprocessDeepSeekContentAdvanced(content string) string {
	return utils.NormalizeText(content)
}

// preprocessDeepSeekContent 预处理DeepSeek内容
func preprocessDeepSeekContent(content string) string {
	// 清理换行符，将多个连续换行符替换为单个换行符
	content = strings.ReplaceAll(content, "\r\n", "\n")
	content = strings.ReplaceAll(content, "\r", "\n")

	// 清理多余的空格
	lines := strings.Split(content, "\n")
	var cleanedLines []string

	for _, line := range lines {
		// 清理每行的前后空格
		line = strings.TrimSpace(line)
		if line != "" {
			// 清理行内多余的空格（保留单个空格）
			line = strings.Join(strings.Fields(line), " ")
			cleanedLines = append(cleanedLines, line)
		}
	}

	return strings.Join(cleanedLines, "\n")
}

// splitAnswerAndAnalysisAdvanced 高级拆分答案和解析部分
func splitAnswerAndAnalysisAdvanced(content string) (string, string) {
	var answerPart, analysisPart string

	// 查找答案和解析的分界点
	answerStart := -1
	analysisStart := -1

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)

		// 查找答案开始位置（支持半角冒号）
		if answerStart == -1 && (strings.HasPrefix(line, "答案:") || strings.HasPrefix(line, "答案：")) {
			answerStart = i
		}

		// 查找解析开始位置（支持半角冒号）
		if strings.HasPrefix(line, "解析:") || strings.HasPrefix(line, "解析：") {
			analysisStart = i
			break
		}
	}

	// 提取答案部分
	if answerStart != -1 {
		endIndex := len(lines)
		if analysisStart != -1 {
			endIndex = analysisStart
		}

		answerLines := lines[answerStart:endIndex]
		// 处理第一行的答案标识
		if len(answerLines) > 0 {
			firstLine := answerLines[0]
			if strings.HasPrefix(firstLine, "答案:") {
				answerLines[0] = strings.TrimSpace(firstLine[3:])
			} else if strings.HasPrefix(firstLine, "答案：") {
				answerLines[0] = strings.TrimSpace(firstLine[3:])
			}
		}
		answerPart = strings.Join(answerLines, " ")
	}

	// 提取解析部分
	if analysisStart != -1 {
		analysisLines := lines[analysisStart:]
		// 处理第一行的解析标识
		if len(analysisLines) > 0 {
			firstLine := analysisLines[0]
			if strings.HasPrefix(firstLine, "解析:") {
				analysisLines[0] = strings.TrimSpace(firstLine[3:])
			} else if strings.HasPrefix(firstLine, "解析：") {
				analysisLines[0] = strings.TrimSpace(firstLine[3:])
			}
		}
		analysisPart = strings.Join(analysisLines, "\n")
	}

	return strings.TrimSpace(answerPart), strings.TrimSpace(analysisPart)
}

// splitAnswerAndAnalysis 拆分答案和解析部分
func splitAnswerAndAnalysis(content string) (string, string) {
	var answerPart, analysisPart string

	// 查找答案和解析的分界点
	answerStart := -1
	analysisStart := -1

	lines := strings.Split(content, "\n")

	for i, line := range lines {
		line = strings.TrimSpace(line)

		// 查找答案开始位置
		if answerStart == -1 && (strings.HasPrefix(line, "答案：") || strings.HasPrefix(line, "答案:")) {
			answerStart = i
		}

		// 查找解析开始位置
		if strings.HasPrefix(line, "解析：") || strings.HasPrefix(line, "解析:") {
			analysisStart = i
			break
		}
	}

	// 提取答案部分
	if answerStart != -1 {
		endIndex := len(lines)
		if analysisStart != -1 {
			endIndex = analysisStart
		}

		answerLines := lines[answerStart:endIndex]
		// 处理第一行的答案标识
		if len(answerLines) > 0 {
			firstLine := answerLines[0]
			if strings.HasPrefix(firstLine, "答案：") {
				answerLines[0] = strings.TrimSpace(firstLine[3:])
			} else if strings.HasPrefix(firstLine, "答案:") {
				answerLines[0] = strings.TrimSpace(firstLine[3:])
			}
		}
		answerPart = strings.Join(answerLines, " ")
	}

	// 提取解析部分
	if analysisStart != -1 {
		analysisLines := lines[analysisStart:]
		// 处理第一行的解析标识
		if len(analysisLines) > 0 {
			firstLine := analysisLines[0]
			if strings.HasPrefix(firstLine, "解析：") {
				analysisLines[0] = strings.TrimSpace(firstLine[3:])
			} else if strings.HasPrefix(firstLine, "解析:") {
				analysisLines[0] = strings.TrimSpace(firstLine[3:])
			}
		}
		analysisPart = strings.Join(analysisLines, "\n")
	}

	return strings.TrimSpace(answerPart), strings.TrimSpace(analysisPart)
}

// extractAnswerByQuestionTypeAdvanced 高级根据题型提取答案
func extractAnswerByQuestionTypeAdvanced(answerPart, analysisPart string) string {
	// 首先尝试从答案部分提取
	if answerPart != "" {
		answer := extractAnswerFromTextAdvanced(answerPart, true)
		if answer != "" {
			return answer
		}
	}

	// 如果答案部分没有找到，尝试从解析部分提取
	if analysisPart != "" {
		answer := extractAnswerFromTextAdvanced(analysisPart, false)
		if answer != "" {
			return answer
		}
	}

	return ""
}

// extractAnswerFromTextAdvanced 高级从文本中提取答案
func extractAnswerFromTextAdvanced(text string, isAnswerSection bool) string {
	// 方法1：尝试解析新格式的答案（方括号格式）
	answer := extractBracketFormatAnswer(text)
	if answer != "" {
		return answer
	}

	// 方法2：尝试标准格式的选项模式
	foundAnswers := extractStandardFormatAnswersAdvanced(text)
	if len(foundAnswers) > 0 {
		return formatAnswerByType(foundAnswers)
	}

	// 方法3：如果还是没找到，尝试从连续文本中提取
	foundAnswers = extractAnswersFromContinuousText(text)
	if len(foundAnswers) > 0 {
		return formatAnswerByType(foundAnswers)
	}

	// 方法4：简单的字母匹配
	foundAnswers = extractSimpleLetterAnswersAdvanced(text)
	if len(foundAnswers) > 0 {
		return formatAnswerByType(foundAnswers)
	}

	return ""
}

// extractBracketFormatAnswer 提取方括号格式的答案
func extractBracketFormatAnswer(text string) string {
	// 查找方括号内的内容
	bracketStart := strings.Index(text, "[")
	bracketEnd := strings.Index(text, "]")

	if bracketStart != -1 && bracketEnd != -1 && bracketEnd > bracketStart {
		bracketContent := text[bracketStart+1 : bracketEnd]

		// 判断题格式: [Y:正确] 或 [N:错误]
		if strings.Contains(bracketContent, "Y:") || strings.Contains(bracketContent, "正确") {
			return "Y"
		}
		if strings.Contains(bracketContent, "N:") || strings.Contains(bracketContent, "错误") {
			return "N"
		}

		// 单选题和多选题格式: [A:内容] 或 [A:内容;B:内容;C:内容]
		var foundAnswers []string

		// 按分号分割多个选项
		parts := strings.Split(bracketContent, ";")
		for _, part := range parts {
			part = strings.TrimSpace(part)
			// 查找选项字母
			for _, char := range "ABCD" {
				if strings.HasPrefix(part, string(char)+":") {
					if !contains(foundAnswers, string(char)) {
						foundAnswers = append(foundAnswers, string(char))
					}
					break
				}
			}
		}

		if len(foundAnswers) > 0 {
			return formatAnswerByType(foundAnswers)
		}
	}

	return ""
}

// extractStandardFormatAnswersAdvanced 高级提取标准格式的答案
func extractStandardFormatAnswersAdvanced(text string) []string {
	var foundAnswers []string

	// 查找"选项X:"格式的答案（支持半角冒号）
	for _, char := range "ABCD" {
		patterns := []string{
			"选项" + string(char) + ":",
			"选项" + string(char) + "：",
			"选项 " + string(char) + ":",
			"选项 " + string(char) + "：",
			string(char) + ":",
			string(char) + "：",
		}

		for _, pattern := range patterns {
			if strings.Contains(text, pattern) {
				// 检查是否包含否定词
				if !containsNegativeWords(text, pattern) {
					if !contains(foundAnswers, string(char)) {
						foundAnswers = append(foundAnswers, string(char))
					}
				}
			}
		}
	}

	// 对于判断题（支持半角冒号）
	if strings.Contains(text, "Y:") || strings.Contains(text, "Y：") ||
	   strings.Contains(text, "正确:") || strings.Contains(text, "正确：") {
		foundAnswers = append(foundAnswers, "Y")
	}
	if strings.Contains(text, "N:") || strings.Contains(text, "N：") ||
	   strings.Contains(text, "错误:") || strings.Contains(text, "错误：") {
		foundAnswers = append(foundAnswers, "N")
	}

	return foundAnswers
}

// extractSimpleLetterAnswersAdvanced 高级简单的字母匹配提取答案
func extractSimpleLetterAnswersAdvanced(text string) []string {
	var foundAnswers []string

	// 查找单独的选项字母
	for _, char := range "ABCD" {
		if strings.Contains(text, string(char)) {
			if !contains(foundAnswers, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	// 对于判断题
	if strings.Contains(text, "Y") || strings.Contains(text, "正确") {
		foundAnswers = append(foundAnswers, "Y")
	}
	if strings.Contains(text, "N") || strings.Contains(text, "错误") {
		foundAnswers = append(foundAnswers, "N")
	}

	return foundAnswers
}

// formatAnswerByType 根据题型格式化答案
func formatAnswerByType(answers []string) string {
	if len(answers) == 0 {
		return ""
	}

	// 对答案进行排序
	sort.Strings(answers)

	// 判断题直接返回
	if len(answers) == 1 && (answers[0] == "Y" || answers[0] == "N") {
		return answers[0]
	}

	// 单选题和多选题返回连续字母
	return strings.Join(answers, "")
}

// processAnalysisPartAdvanced 高级处理解析部分
func processAnalysisPartAdvanced(analysisPart, fullContent string) string {
	if analysisPart != "" {
		return analysisPart
	}

	// 如果没有明确的解析部分，使用完整内容
	return fullContent
}

// extractAnswerByQuestionType 根据题型提取答案
func extractAnswerByQuestionType(answerPart, analysisPart string) string {
	// 首先尝试从答案部分提取
	if answerPart != "" {
		answer := extractAnswerFromText(answerPart, true)
		if answer != "" {
			return answer
		}
	}

	// 如果答案部分没有找到，尝试从解析部分提取
	if analysisPart != "" {
		answer := extractAnswerFromText(analysisPart, false)
		if answer != "" {
			return answer
		}
	}

	return ""
}

// extractAnswerFromText 从文本中提取答案
func extractAnswerFromText(text string, isAnswerSection bool) string {
	var foundAnswers []string

	// 方法1：查找标准格式的选项模式
	foundAnswers = extractStandardFormatAnswers(text)

	// 方法2：如果标准格式没找到，尝试从连续文本中提取
	if len(foundAnswers) == 0 {
		foundAnswers = extractAnswersFromContinuousText(text)
	}

	// 方法3：如果还是没找到，尝试简单的字母匹配
	if len(foundAnswers) == 0 {
		foundAnswers = extractSimpleLetterAnswers(text)
	}

	// 处理多选题答案格式
	if len(foundAnswers) > 1 {
		return formatMultipleChoiceAnswer(foundAnswers)
	} else if len(foundAnswers) == 1 {
		return foundAnswers[0]
	}

	return ""
}

// extractStandardFormatAnswers 提取标准格式的答案
func extractStandardFormatAnswers(text string) []string {
	var foundAnswers []string

	// 查找"选项X:"格式的答案
	for _, char := range "ABCD" {
		patterns := []string{
			"选项" + string(char) + ":",
			"选项" + string(char) + "：",
			"选项 " + string(char) + ":",
			"选项 " + string(char) + "：",
			string(char) + ":",
			string(char) + "：",
		}

		for _, pattern := range patterns {
			if strings.Contains(text, pattern) {
				// 检查是否包含否定词
				if !containsNegativeWords(text, pattern) {
					if !contains(foundAnswers, string(char)) {
						foundAnswers = append(foundAnswers, string(char))
					}
				}
			}
		}
	}

	// 对于判断题
	if strings.Contains(text, "Y:") || strings.Contains(text, "Y：") ||
	   strings.Contains(text, "正确:") || strings.Contains(text, "正确：") {
		foundAnswers = append(foundAnswers, "Y")
	}
	if strings.Contains(text, "N:") || strings.Contains(text, "N：") ||
	   strings.Contains(text, "错误:") || strings.Contains(text, "错误：") {
		foundAnswers = append(foundAnswers, "N")
	}

	return foundAnswers
}

// extractSimpleLetterAnswers 简单的字母匹配提取答案
func extractSimpleLetterAnswers(text string) []string {
	var foundAnswers []string

	// 查找单独的选项字母
	for _, char := range "ABCD" {
		if strings.Contains(text, string(char)) {
			if !contains(foundAnswers, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	// 对于判断题
	if strings.Contains(text, "Y") || strings.Contains(text, "正确") {
		foundAnswers = append(foundAnswers, "Y")
	}
	if strings.Contains(text, "N") || strings.Contains(text, "错误") {
		foundAnswers = append(foundAnswers, "N")
	}

	return foundAnswers
}

// formatMultipleChoiceAnswer 格式化多选题答案
func formatMultipleChoiceAnswer(answers []string) string {
	// 对答案进行排序
	sort.Strings(answers)

	// 多选题答案换行处理
	if len(answers) > 1 {
		return strings.Join(answers, "")
	}

	return strings.Join(answers, "")
}

// processAnalysisPart 处理解析部分
func processAnalysisPart(analysisPart, fullContent string) string {
	if analysisPart != "" {
		return analysisPart
	}

	// 如果没有明确的解析部分，使用完整内容
	return fullContent
}

// containsNegativeWords 检查是否包含否定词
func containsNegativeWords(text, pattern string) bool {
	// 查找包含该模式的句子或段落
	index := strings.Index(text, pattern)
	if index == -1 {
		return false
	}

	// 检查前后文是否包含否定词
	start := max(0, index-50)
	end := min(len(text), index+len(pattern)+50)
	context := text[start:end]

	negativeWords := []string{
		"不正确", "错误", "不成立", "不符合", "不选", "因此不选",
		"不是", "不对", "不当", "不合适", "不合理",
	}

	for _, word := range negativeWords {
		if strings.Contains(context, word) {
			return true
		}
	}

	return false
}

// contains 检查字符串切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// max 返回两个整数的最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 返回两个整数的最小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// removeReferenceSource 移除"参考来源"以及相关参考信息后面的所有内容
func removeReferenceSource(analysis string) string {
	// 查找"参考来源"的位置
	index := strings.Index(analysis, "参考来源")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"此答案参考"的位置
	index = strings.Index(analysis, "此答案参考")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 查找"参考自"的位置
	index = strings.Index(analysis, "参考自")
	if index != -1 {
		return strings.TrimSpace(analysis[:index])
	}

	// 如果没有找到任何参考关键词，返回原内容
	return analysis
}

// ParseQwenExtraction 解析Qwen提取的题目信息（导出函数）
func ParseQwenExtraction(content string) (*models.QuestionData, error) {
	return parseQwenExtraction(content)
}

// ParseDeepSeekResponse 解析DeepSeek的答案分析（导出函数）
func ParseDeepSeekResponse(content string) (string, string, error) {
	return parseDeepSeekAnswer(content)
}

// extractAnswersFromContinuousText 从连续文本中提取答案选项
func extractAnswersFromContinuousText(text string) []string {
	var foundAnswers []string

	// 根据选项内容判断是否包含该选项
	for _, char := range "ABCD" {
		switch char {
		case 'A':
			if strings.Contains(text, "超过额定乘员20%") && strings.Contains(text, "500元以上2000") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'B':
			if strings.Contains(text, "运输单位") && strings.Contains(text, "2000 元以 上 5000") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'C':
			if strings.Contains(text, "公安机关交通管理部门") && strings.Contains(text, "扣留机动车") {
				foundAnswers = append(foundAnswers, string(char))
			}
		case 'D':
			if strings.Contains(text, "未达") && strings.Contains(text, "200 元") {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	// 如果还是没有找到，尝试更通用的方法：查找选项关键词
	if len(foundAnswers) == 0 {
		// 查找可能的选项指示词
		for _, char := range "ABCD" {
			// 查找选项字母后跟冒号或其他分隔符的模式
			if strings.Contains(text, string(char)+":") ||
			   strings.Contains(text, string(char)+"：") ||
			   strings.Contains(text, "选项"+string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}
	}

	return foundAnswers
}

// extractAnswerFromContent 从内容中智能提取答案（备用方法）
func extractAnswerFromContent(content string) string {
	// 首先尝试在"答案："部分查找
	answerSection := extractAnswerSection(content)
	if answerSection != "" {
		result := extractAnswerFromText(answerSection, true)
		if result != "" {
			return result
		}
	}

	// 如果答案部分没有找到，使用原有的逻辑作为备用
	var foundAnswers []string
	lines := strings.Split(content, "\n")

	for _, line := range lines {
		if strings.Contains(line, "选项") {
			// 提取选项字母
			for _, char := range "ABCD" {
				if strings.Contains(line, "选项"+string(char)) ||
				   strings.Contains(line, "选项 "+string(char)) {
					// 检查这一行是否表示这是正确答案
					if !strings.Contains(line, "不正确") &&
					   !strings.Contains(line, "错误") &&
					   !strings.Contains(line, "不成立") &&
					   !strings.Contains(line, "不符合") &&
					   !strings.Contains(line, "不选") {
						found := false
						for _, existing := range foundAnswers {
							if existing == string(char) {
								found = true
								break
							}
						}
						if !found {
							foundAnswers = append(foundAnswers, string(char))
						}
					}
				}
			}
		}
	}

	// 如果没有找到明确的选项引用，使用简单的字母检测
	if len(foundAnswers) == 0 {
		for _, char := range "ABCD" {
			if strings.Contains(content, string(char)) {
				foundAnswers = append(foundAnswers, string(char))
			}
		}

		// 对于判断题
		if strings.Contains(content, "Y") || strings.Contains(content, "正确") {
			foundAnswers = append(foundAnswers, "Y")
		}
		if strings.Contains(content, "N") || strings.Contains(content, "错误") {
			foundAnswers = append(foundAnswers, "N")
		}
	}

	// 返回找到的答案
	if len(foundAnswers) > 0 {
		return strings.Join(foundAnswers, "")
	}

	return ""
}

// extractAnswerSection 提取答案部分的内容
func extractAnswerSection(content string) string {
	lines := strings.Split(content, "\n")
	var answerLines []string
	inAnswerSection := false

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		if strings.HasPrefix(line, "答案：") || strings.HasPrefix(line, "答案:") {
			inAnswerSection = true
			// 如果答案在同一行
			if len(line) > 3 {
				answerValue := strings.TrimSpace(line[3:])
				if answerValue != "" {
					answerLines = append(answerLines, answerValue)
				}
			}
			continue
		} else if strings.HasPrefix(line, "解析：") || strings.HasPrefix(line, "解析:") {
			break // 答案部分结束
		}

		if inAnswerSection {
			answerLines = append(answerLines, line)
		}
	}

	return strings.Join(answerLines, "\n")
}

// normalizeOptionContent 标准化选项内容
// 注意：此函数已被utils.NormalizeText替代，保留用于兼容性
func normalizeOptionContent(content string) string {
	return utils.NormalizeText(content)
}

// cleanUTF8StringDeepSeek 清理UTF-8字符串
// 注意：此函数已被utils.CleanUTF8String替代，保留用于兼容性
func cleanUTF8StringDeepSeek(s string) string {
	return utils.CleanUTF8String(s)
}

// getDeepSeekAPIKey 从环境变量获取DeepSeek API Key
func getDeepSeekAPIKey() string {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		// 如果环境变量未设置，使用默认值（仅用于开发环境）
		apiKey = "sk-dd3347aa018244b1a2e19bb364c3c97e"
	}
	return apiKey
}