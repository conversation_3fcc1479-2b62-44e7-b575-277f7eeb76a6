package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type SolveRequest struct {
	QuType string `json:"qu_type"`
	ImgURL string `json:"img_url"`
}

func main() {
	// API配置
	apiURL := "http://47.96.29.178:8080/api/solve"
	apiKey := "ak_27a6480d6a695cb53b6a0a955e2930d9b639a3d225ebb10fdefa8f33bb67d8eb"
	imageURL := "https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg"

	fmt.Println("=== 测试Qwen原始数据输出（缓存已清空） ===")
	fmt.Printf("时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Printf("图片URL: %s\n", imageURL)
	fmt.Println("开始调用API，请查看服务器控制台输出...")
	fmt.Println()

	// 创建请求数据
	requestData := SolveRequest{
		QuType: "1",
		ImgURL: imageURL,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		fmt.Printf("❌ JSON编码失败: %v\n", err)
		return
	}

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ 创建请求失败: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: 90 * time.Second,
	}

	// 记录开始时间
	startTime := time.Now()
	fmt.Printf("⏰ 请求开始时间: %s\n", startTime.Format("15:04:05.000"))

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 记录响应时间
	endTime := time.Now()
	duration := endTime.Sub(startTime)
	fmt.Printf("⏰ 请求完成时间: %s\n", endTime.Format("15:04:05.000"))
	fmt.Printf("⏱️  总耗时: %v\n", duration)
	fmt.Println()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return
	}

	fmt.Printf("📊 响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("📦 响应内容长度: %d 字节\n", len(body))
	
	if resp.StatusCode == 200 {
		fmt.Println("✅ API调用成功！")
		fmt.Println()
		fmt.Println("🔍 请查看服务器控制台，应该能看到:")
		fmt.Println("   1. Qwen原始响应数据（带有分隔线的格式化输出）")
		fmt.Println("   2. DeepSeek原始响应数据")
		fmt.Println("   3. 详细的日志记录")
		fmt.Println()
		fmt.Println("📁 同时检查日志文件: logs/api_details.log")
	} else {
		fmt.Printf("❌ API调用失败: %s\n", string(body))
	}
}
