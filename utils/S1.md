## S1需求：标点符号规范化工具

**需求描述：**
使用一个映射表 + 字符遍历替换的方式来实现，确保不会乱码，并保持可控性。一劳永逸的方法解决标点符号转码以及防止乱码的问题。

**实现状态：✅ 已完成**

### 实现方案

1. **创建统一的标点符号规范化工具** (`utils/text_normalizer.go`)
   - 使用rune映射表确保不会乱码
   - 支持全角到半角标点符号转换
   - 提供多种规范化级别的函数

2. **替换项目中现有的多个不同实现**
   - 更新 `services/deepseek_service.go` 使用统一工具
   - 更新 `services/qwen_service.go` 使用统一工具
   - 更新 `services/redis_cache.go` 使用统一工具


package main

import (
	"fmt"
	"strings"
)

var punctuationMap = map[rune]rune{
	'，': ',', '。': '.', '！': '!', '？': '?',
	'：': ':', '；': ';', '“': '"', '”': '"',
	'‘': '\'', '’': '\'', '（': '(', '）': ')',
	'【': '[', '】': ']', '《': '<', '》': '>',
	'、': ',', '～': '~', '—': '-', '　': ' ', // 全角空格
}

func NormalizePunctuation(text string) string {
	var builder strings.Builder
	for _, ch := range text {
		if mapped, ok := punctuationMap[ch]; ok {
			builder.WriteRune(mapped)
		} else {
			builder.WriteRune(ch)
		}
	}
	return builder.String()
}

func main() {
	input := `“你好，世界！” 你（好吗）？　今天是：2025年6月4日。`
	output := NormalizePunctuation(input)
	fmt.Println(output)
}



API测试数据：


API Key: ak_27a6480d6a695cb53b6a0a955e2930d9b639a3d225ebb10fdefa8f33bb67d8eb



在最终的解析json中，我发现了一个非预期的效果。answer中出现了[]这个符号是用来提醒在之前的业务中格式的。而非真实需要的。
另外在多选题的业务中，我的预期是多个答案之间换行。


在最终的解析json中，我发现了一个非预期的效果。answer中出现了[]这个符号是用来提醒在之前的业务中格式的。而非真实需要的。
另外在多选题的业务中，我的预期是多个答案之间换行。



帮我添加一些日志输出到log文件，
1.qwen返回的原始数据；
2.deepseek返回的原始数据；
3.qwen原始数据解析后的json；
4.deepseek原始数据解析后的json；



缓存的逻辑应该是，
qwen返回原始数据