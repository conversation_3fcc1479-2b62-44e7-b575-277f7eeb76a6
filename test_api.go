package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

type SolveRequest struct {
	QuType string `json:"qu_type"`
	ImgURL string `json:"img_url"`
}

type SolveResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

func main() {
	// API配置
	apiURL := "http://47.96.29.178:8080/api/solve"
	apiKey := "ak_27a6480d6a695cb53b6a0a955e2930d9b639a3d225ebb10fdefa8f33bb67d8eb"
	imageURL := "https://www.bonuspoints.uzdns.com/20250604132258f988d2353.jpg"

	// 创建请求数据
	requestData := SolveRequest{
		QuType: "1",
		ImgURL: imageURL,
	}

	// 转换为JSON
	jsonData, err := json.Marshal(requestData)
	if err != nil {
		fmt.Printf("JSON编码失败: %v\n", err)
		return
	}

	fmt.Printf("请求数据: %s\n", string(jsonData))
	fmt.Printf("API Key: %s\n", apiKey)
	fmt.Printf("图片URL: %s\n", imageURL)
	fmt.Println("开始调用API...")

	// 创建HTTP请求
	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("创建请求失败: %v\n", err)
		return
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+apiKey)

	// 创建HTTP客户端并设置超时
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	// 发送请求
	fmt.Println("正在发送请求...")
	resp, err := client.Do(req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("读取响应失败: %v\n", err)
		return
	}

	fmt.Printf("响应状态码: %d\n", resp.StatusCode)
	fmt.Printf("响应内容: %s\n", string(body))

	// 解析响应JSON
	var response SolveResponse
	if err := json.Unmarshal(body, &response); err != nil {
		fmt.Printf("解析响应JSON失败: %v\n", err)
		return
	}

	// 格式化输出
	fmt.Println("\n=== API调用结果 ===")
	fmt.Printf("状态码: %d\n", response.Code)
	fmt.Printf("消息: %s\n", response.Message)
	
	if response.Data != nil {
		dataJSON, _ := json.MarshalIndent(response.Data, "", "  ")
		fmt.Printf("数据: %s\n", string(dataJSON))
	}
}
